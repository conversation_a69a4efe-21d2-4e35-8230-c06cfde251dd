NOTIFICATION SERVICE - ДОКУМЕНТАЦИЯ ДЛЯ FRONTEND РАЗРАБОТЧИКА
================================================================

ВАЖНО: Была исправлена критическая ошибка в обработке email шаблонов!
Ранее при указании email_template в теле email отображалось только название шаблона.
Теперь система корректно загружает и обрабатывает шаблоны из базы данных.

BASE URL: http://localhost:8081

АВТОРИЗАЦИЯ
===========
Все запросы требуют заголовок Authorization с Bearer токеном:
Authorization: Bearer <your_jwt_token>

РОЛИ И ДОСТУП
=============
- admin: полный доступ ко всем операциям
- teacher: может создавать уведомления через /teacher/notifications
- student: может просматривать только свои уведомления

1. СОЗДАНИЕ УВЕДОМЛЕНИЯ (АДМИНЫ)
===============================
POST /notifications

Headers:
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "title": "Заголовок уведомления",
  "message": "Текст сообщения", 
  "type": "info",
  "priority": "normal",
  "target_type": "all",
  "target_value": "",
  "send_email": true,
  "email_subject": "Тема письма",
  "email_template": "default_notification",
  "scheduled_at": "2024-01-15T10:00:00Z"
}

ПАРАМЕТРЫ:
- title (обязательно): заголовок уведомления
- message (обязательно): текст сообщения
- type: "info", "warning", "success", "error", "announcement"
- priority: "low", "normal", "high", "urgent"
- target_type (обязательно): "all", "role", "user", "degree", "course", "thread"
- target_value: значение цели (например "student" для роли, "123" для user_id)
- send_email: true/false - отправлять ли email
- email_subject: тема email (если send_email = true)
- email_template: имя шаблона из базы данных
- scheduled_at: время отложенной отправки (RFC3339 формат)

ДОСТУПНЫЕ EMAIL ШАБЛОНЫ:
- default_notification: стандартный шаблон
- announcement: для объявлений  
- urgent_notification: для срочных уведомлений
- class_reminder: напоминания о занятиях
- assignment_notification: уведомления о заданиях
- grade_notification: уведомления об оценках
- welcome_student: приветствие новых студентов

Response 201:
{
  "notification": {
    "id": 1,
    "title": "Заголовок уведомления",
    "message": "Текст сообщения",
    "type": 0,
    "priority": 1,
    "target_type": 0,
    "target_value": "",
    "sender_id": 123,
    "send_email": true,
    "email_subject": "Тема письма",
    "email_template": "default_notification",
    "scheduled_at": "2024-01-15T10:00:00Z",
    "sent_at": null,
    "created_at": "2024-01-15T09:00:00Z",
    "updated_at": "2024-01-15T09:00:00Z"
  },
  "message": "Notification created successfully",
  "success": true
}

2. СОЗДАНИЕ УВЕДОМЛЕНИЯ (ПРЕПОДАВАТЕЛИ)
======================================
POST /teacher/notifications

Тот же формат запроса и ответа, что и для админов.
Преподаватели могут создавать уведомления для своих курсов/потоков.

3. ПОЛУЧЕНИЕ УВЕДОМЛЕНИЙ ПОЛЬЗОВАТЕЛЯ
====================================
GET /users/{user_id}/notifications

Query Parameters:
- page: номер страницы (по умолчанию: 1)
- limit: количество на странице (по умолчанию: 20)
- unread_only: только непрочитанные (true/false, по умолчанию: false)

Response 200:
{
  "notifications": [
    {
      "notification": {
        "id": 1,
        "title": "Заголовок уведомления",
        "message": "Текст сообщения",
        "type": 0,
        "priority": 1,
        "target_type": 0,
        "target_value": "",
        "sender_id": 123,
        "send_email": true,
        "created_at": "2024-01-15T09:00:00Z"
      },
      "recipient": {
        "id": 1,
        "notification_id": 1,
        "user_id": 456,
        "is_read": false,
        "read_at": null,
        "email_sent": true,
        "email_sent_at": "2024-01-15T09:01:00Z",
        "created_at": "2024-01-15T09:00:00Z"
      }
    }
  ],
  "total_count": 15,
  "unread_count": 3
}

4. ПОЛУЧЕНИЕ ВСЕХ УВЕДОМЛЕНИЙ (АДМИНЫ)
=====================================
GET /notifications

Query Parameters:
- page: номер страницы (по умолчанию: 1)
- limit: количество на странице (по умолчанию: 20)
- target_type: фильтр по типу получателей
- target_value: фильтр по значению цели

Response 200:
{
  "notifications": [
    {
      "id": 1,
      "title": "Заголовок уведомления",
      "message": "Текст сообщения",
      "type": 0,
      "priority": 1,
      "target_type": 0,
      "target_value": "",
      "sender_id": 123,
      "send_email": true,
      "created_at": "2024-01-15T09:00:00Z"
    }
  ],
  "total_count": 50
}

5. ОТМЕТИТЬ КАК ПРОЧИТАННОЕ
===========================
PUT /notifications/{notification_id}/read

Response 200:
{
  "success": true,
  "message": "Notification marked as read"
}

6. УДАЛЕНИЕ УВЕДОМЛЕНИЯ (АДМИНЫ)
===============================
DELETE /notifications/{notification_id}

Response 200:
{
  "success": true,
  "message": "Notification deleted successfully"
}

7. СТАТИСТИКА УВЕДОМЛЕНИЙ
========================
GET /users/{user_id}/notifications/stats

Response 200:
{
  "total_notifications": 25,
  "unread_notifications": 3,
  "read_notifications": 22,
  "email_notifications": 15
}

8. ПОЛУЧЕНИЕ EMAIL ШАБЛОНОВ (АДМИНЫ)
===================================
GET /email-templates

Query Parameters:
- active_only: только активные шаблоны (true/false, по умолчанию: true)

Response 200:
{
  "templates": [
    {
      "id": 1,
      "name": "default_notification",
      "subject": "Новое уведомление - {{title}}",
      "html_content": "<html>...</html>",
      "text_content": "{{title}}\n\n{{message}}...",
      "variables": {
        "title": "Заголовок уведомления",
        "message": "Текст сообщения"
      },
      "is_active": true,
      "created_at": "2024-01-15T09:00:00Z",
      "updated_at": "2024-01-15T09:00:00Z"
    }
  ],
  "total_count": 7
}

КОДЫ ТИПОВ И ПРИОРИТЕТОВ
========================
Type (числовые значения в ответах):
0 = info
1 = warning  
2 = success
3 = error
4 = announcement

Priority (числовые значения в ответах):
0 = low
1 = normal
2 = high
3 = urgent

Target Type (числовые значения в ответах):
0 = all
1 = role
2 = user
3 = degree
4 = course
5 = thread

КОДЫ ОШИБОК
===========
400 Bad Request - неверные параметры запроса
401 Unauthorized - пользователь не аутентифицирован
403 Forbidden - недостаточно прав доступа
404 Not Found - уведомление не найдено
500 Internal Server Error - внутренняя ошибка сервера

ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ
====================

1. Объявление для всех:
POST /notifications
{
  "title": "Важное объявление",
  "message": "Завтра техническое обслуживание с 02:00 до 06:00",
  "type": "announcement",
  "priority": "high",
  "target_type": "all",
  "send_email": true,
  "email_template": "announcement"
}

2. Уведомление для студентов:
POST /notifications
{
  "title": "Напоминание",
  "message": "Не забудьте сдать курсовые работы",
  "type": "info",
  "priority": "normal",
  "target_type": "role",
  "target_value": "student",
  "send_email": false
}

3. Срочное уведомление конкретному пользователю:
POST /notifications
{
  "title": "Срочно",
  "message": "Ваша заявка требует рассмотрения",
  "type": "error",
  "priority": "urgent",
  "target_type": "user",
  "target_value": "123",
  "send_email": true,
  "email_template": "urgent_notification"
}

ВАЖНЫЕ ЗАМЕЧАНИЯ ДЛЯ FRONTEND
============================

1. ИСПРАВЛЕНИЕ EMAIL ШАБЛОНОВ:
   - Ранее при указании email_template в email отображалось только название
   - Теперь система корректно загружает HTML шаблоны из базы данных
   - Переменные {{title}}, {{message}}, {{user_name}} заменяются на реальные значения

2. ВАЛИДАЦИЯ:
   - title и message обязательны
   - target_type обязателен
   - При send_email=true рекомендуется указать email_subject и email_template

3. ФОРМАТЫ ДАТА/ВРЕМЯ:
   - Используйте RFC3339 формат для scheduled_at: "2024-01-15T10:00:00Z"

4. ПАГИНАЦИЯ:
   - Все списки поддерживают пагинацию через page и limit
   - Максимальный limit обычно 100

5. ФИЛЬТРАЦИЯ:
   - Уведомления можно фильтровать по target_type и target_value
   - Поддерживается фильтр только непрочитанных (unread_only=true)

6. REAL-TIME ОБНОВЛЕНИЯ:
   - Рекомендуется периодически обновлять счетчик непрочитанных
   - Используйте endpoint /users/{user_id}/notifications/stats

7. ОБРАБОТКА ОШИБОК:
   - Всегда проверяйте поле "success" в ответе
   - При success=false смотрите поле "message" для описания ошибки

ТЕСТИРОВАНИЕ
============

Для тестирования API можно использовать следующие curl команды:

1. Получить список шаблонов:
curl -H "Authorization: Bearer <token>" http://localhost:8081/email-templates

2. Создать тестовое уведомление:
curl -X POST http://localhost:8081/notifications \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Тест",
    "message": "Тестовое сообщение",
    "type": "info",
    "target_type": "all",
    "send_email": true,
    "email_template": "default_notification"
  }'

3. Получить уведомления пользователя:
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8081/users/1/notifications?page=1&limit=10"

КОНТАКТЫ
========
При возникновении проблем с API обращайтесь к backend разработчику.
Все изменения в API документируются в этом файле.
