# Test script for Thread Gradebook API endpoint
# Тестовый скрипт для проверки endpoint'а журнала оценок потока

# Configuration
$baseUrl = "http://localhost:8080"
$threadId = 1  # Change this to an existing thread ID in your database

# Headers (add authorization if needed)
$headers = @{
    "Content-Type" = "application/json"
    # "Authorization" = "Bearer YOUR_TOKEN_HERE"  # Uncomment and add token if auth is required
}

Write-Host "Testing Thread Gradebook API..." -ForegroundColor Green
Write-Host "URL: $baseUrl/assignments/thread/$threadId/gradebook" -ForegroundColor Yellow

try {
    # Make the request
    $response = Invoke-RestMethod -Uri "$baseUrl/assignments/thread/$threadId/gradebook" -Method GET -Headers $headers
    
    Write-Host "✅ Request successful!" -ForegroundColor Green
    Write-Host ""
    
    # Display results
    Write-Host "📚 Assignments found: $($response.assignments.Count)" -ForegroundColor Cyan
    if ($response.assignments.Count -gt 0) {
        Write-Host "Assignments:" -ForegroundColor White
        foreach ($assignment in $response.assignments) {
            Write-Host "  - ID: $($assignment.id), Title: '$($assignment.title)', Max Points: $($assignment.max_points)" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    Write-Host "👥 Students found: $($response.students.Count)" -ForegroundColor Cyan
    if ($response.students.Count -gt 0) {
        Write-Host "Students:" -ForegroundColor White
        foreach ($student in $response.students) {
            $finalGrade = if ($student.final_grade -ne $null) { $student.final_grade } else { "No final grade" }
            Write-Host "  - ID: $($student.id), Name: $($student.name) $($student.surname), Final Grade: $finalGrade" -ForegroundColor Gray
            
            if ($student.grades.Count -gt 0) {
                Write-Host "    Grades:" -ForegroundColor DarkGray
                foreach ($grade in $student.grades) {
                    $score = if ($grade.PSObject.Properties.Name -contains "score") { $grade.score } else { "No grade" }
                    Write-Host "      Assignment $($grade.assignment_id): $score" -ForegroundColor DarkGray
                }
            }
        }
    }
    
    Write-Host ""
    Write-Host "📄 Full Response:" -ForegroundColor Magenta
    $response | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor DarkGray
    
} catch {
    Write-Host "❌ Request failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "1. Make sure the gateway service is running on port 8080" -ForegroundColor Gray
Write-Host "2. Make sure the course_service is running and connected" -ForegroundColor Gray
Write-Host "3. Check that thread ID $threadId exists in your database" -ForegroundColor Gray
Write-Host "4. If using authentication, uncomment and set the Authorization header" -ForegroundColor Gray
Write-Host "5. Check the database has some test data (threads, assignments, students, grades)" -ForegroundColor Gray
