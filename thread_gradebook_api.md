# Thread Gradebook API

## Описание

Новый endpoint для получения журнала оценок потока со всеми заданиями и оценками студентов.

## Endpoint

```
GET /assignments/thread/{threadId}/gradebook
```

## Параметры

- `threadId` (path parameter) - ID потока (обязательный)

## Ответ

Возвращает JSON объект с информацией о заданиях и оценках студентов:

```json
{
  "assignments": [
    {
      "id": 101,
      "title": "Домашнее задание 1",
      "max_points": 12
    },
    {
      "id": 102,
      "title": "Домашнее задание 2", 
      "max_points": 15
    },
    {
      "id": 103,
      "title": "Итоговый проект",
      "max_points": 25
    }
  ],
  "students": [
    {
      "id": 1,
      "name": "<PERSON><PERSON><PERSON><PERSON>",
      "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      "grades": [
        {
          "assignment_id": 101,
          "score": 12
        },
        {
          "assignment_id": 102,
          "score": 13
        },
        {
          "assignment_id": 103,
          "score": 22
        }
      ],
      "final_grade": 47
    },
    {
      "id": 2,
      "name": "Петр",
      "surname": "Петров",
      "grades": [
        {
          "assignment_id": 101,
          "score": 10
        },
        {
          "assignment_id": 102,
          "score": 15
        },
        {
          "assignment_id": 103,
          "score": 25
        }
      ],
      "final_grade": 50
    },
    {
      "id": 3,
      "name": "Анна",
      "surname": "Сидорова",
      "grades": [
        {
          "assignment_id": 101,
          "score": 11
        },
        {
          "assignment_id": 102,
          "score": 14
        }
      ],
      "final_grade": null
    }
  ]
}
```

## Примеры запросов

### cURL

```bash
curl -X GET "http://localhost:8080/assignments/thread/1/gradebook" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### PowerShell

```powershell
$headers = @{
    "Authorization" = "Bearer YOUR_TOKEN"
    "Content-Type" = "application/json"
}

Invoke-RestMethod -Uri "http://localhost:8080/assignments/thread/1/gradebook" -Method GET -Headers $headers
```

## Особенности

1. **Только задания типа 'task'** - endpoint возвращает только задания с типом 'task', исключая информационные задания
2. **Только студенты** - возвращаются только пользователи с ролью 'student'
3. **Оценки могут отсутствовать** - если у студента нет оценки за задание, поле `score` не включается в объект grade
4. **Финальная оценка может быть null** - если у студента нет финальной оценки, поле `final_grade` будет null
5. **Сортировка** - задания сортируются по номеру недели и ID, студенты по фамилии и имени

## Коды ошибок

- `400 Bad Request` - неверный ID потока
- `500 Internal Server Error` - ошибка сервера при получении данных

## Логирование

Endpoint логирует ошибки через RabbitMQ с типом "get_thread_gradebook" для отладки.

## Архитектура

Endpoint реализован через:
1. **Gateway Handler** - `GetThreadGradebookHandler` в `assignment_handler.go`
2. **Assignment Client** - `GetThreadGradebook` метод для вызова gRPC
3. **Assignment Service** - `GetThreadGradebook` метод в course_service
4. **Query Repository** - `GetThreadGradebook` для сложных SQL запросов
5. **DTO структуры** - для типизированной передачи данных

## База данных

Endpoint выполняет три основных запроса:
1. Получение всех заданий потока (только type='task')
2. Получение всех студентов потока с финальными оценками
3. Получение всех оценок студентов за задания

Данные объединяются в памяти для формирования итогового ответа.
