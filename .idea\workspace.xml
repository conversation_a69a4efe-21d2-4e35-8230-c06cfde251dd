<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4d977e9b-a02f-448c-9aee-38c8d803f5cc" name="Changes" comment="upt">
      <change beforePath="$PROJECT_DIR$/course_service/internal/dto/assignment.go" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/internal/dto/assignment.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/course_service/internal/query/assignmentquery/assignments_with_submissions.go" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/internal/query/assignmentquery/assignments_with_submissions.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/course_service/internal/service/assignment/assignment.go" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/internal/service/assignment/assignment.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/course_service/pb/assignment/assignment.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/pb/assignment/assignment.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/course_service/pb/assignment/assignment.proto" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/pb/assignment/assignment.proto" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/course_service/pb/assignment/assignment_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/course_service/pb/assignment/assignment_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gateway/clients/assignment_client.go" beforeDir="false" afterPath="$PROJECT_DIR$/gateway/clients/assignment_client.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gateway/gateway.exe" beforeDir="false" afterPath="$PROJECT_DIR$/gateway/gateway.exe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gateway/handlers/assignment_handler.go" beforeDir="false" afterPath="$PROJECT_DIR$/gateway/handlers/assignment_handler.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gateway/routes/assignment.go" beforeDir="false" afterPath="$PROJECT_DIR$/gateway/routes/assignment.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dockerfile" />
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Olzzhas&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Olzzhas/edunite-server.git&quot;,
    &quot;accountId&quot;: &quot;55e644b7-88cc-498f-9cfd-1bb5922c3918&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/course_service/cmd/main.go" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/!nerzal/gocloak/v13@v13.9.0/client.go" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2nehMdmoI1xsckcuA0SpDnZYs20" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultGoTemplateProperty&quot;: &quot;Go File&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/edunite-server/logger_service&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\edunite-server\logger_service" />
      <recent name="C:\Users\<USER>\Desktop\edunite-server\gateway" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\edunite-server\course_service\pb\thread" />
      <recent name="C:\Users\<USER>\Desktop\edunite-server\course_service\pb\semester" />
      <recent name="C:\Users\<USER>\Desktop\edunite-server\course_service\pb\course" />
      <recent name="C:\Users\<USER>\Desktop\edunite-server\course_service\pb\attendance" />
      <recent name="C:\Users\<USER>\Desktop\edunite-server\course_service\pb\assignment" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-5df93f7ad4aa-df9ad98b711f-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-242.24807.19" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-GO-242.24807.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix get user" />
    <MESSAGE value="auth fix" />
    <MESSAGE value="fix docker compose" />
    <MESSAGE value="cors" />
    <MESSAGE value="storage service fix ports" />
    <MESSAGE value="upt" />
    <option name="LAST_COMMIT_MESSAGE" value="upt" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>