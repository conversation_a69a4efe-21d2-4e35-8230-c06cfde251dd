package email

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"strings"

	"github.com/olzzhas/edunite-server/notification_service/internal/config"
	"github.com/olzzhas/edunite-server/notification_service/internal/models"
	"gopkg.in/gomail.v2"
)

type EmailService interface {
	SendNotificationEmail(notification *models.Notification, user *models.User, emailTemplate *models.EmailTemplate) error
	SendBulkNotificationEmails(notification *models.Notification, users []*models.User, emailTemplate *models.EmailTemplate) error
}

type emailService struct {
	config *config.Config
	dialer *gomail.Dialer
}

func NewEmailService(cfg *config.Config) EmailService {
	dialer := gomail.NewDialer(
		"sandbox.smtp.mailtrap.io",
		587,
		"bf7648992c1e23",
		"04a153132942ec",
	)
	//dialer := gomail.NewDialer(
	//	cfg.Email.SMTPHost,
	//	cfg.Email.SMTPPort,
	//	cfg.Email.SMTPUsername,
	//	cfg.Email.SMTPPassword,
	//)

	return &emailService{
		config: cfg,
		dialer: dialer,
	}
}

func (s *emailService) SendNotificationEmail(notification *models.Notification, user *models.User, emailTemplate *models.EmailTemplate) error {
	// Prepare template variables
	variables := map[string]interface{}{
		"title":     notification.Title,
		"message":   notification.Message,
		"user_name": user.Name + " " + user.Surname,
		"user_id":   user.ID,
	}

	// Parse template variables from database if available
	if emailTemplate.Variables != nil {
		var templateVars map[string]interface{}
		if err := json.Unmarshal([]byte(*emailTemplate.Variables), &templateVars); err == nil {
			// Merge template variables with notification variables
			for k, v := range templateVars {
				if _, exists := variables[k]; !exists {
					variables[k] = v
				}
			}
		}
	}

	// Process email subject
	subject := notification.Title
	if notification.EmailSubject != nil && *notification.EmailSubject != "" {
		subject = *notification.EmailSubject
	} else if emailTemplate != nil {
		subject = emailTemplate.Subject
	}
	subject = s.processTemplate(subject, variables)

	// Process email content
	var htmlContent, textContent string

	if emailTemplate != nil {
		// Use template from database
		htmlContent = s.processTemplate(emailTemplate.HTMLContent, variables)
		if emailTemplate.TextContent != nil {
			textContent = s.processTemplate(*emailTemplate.TextContent, variables)
		} else {
			textContent = s.stripHTML(emailTemplate.HTMLContent)
		}
	} else {
		// Use default template
		htmlContent = fmt.Sprintf(`
			<html>
			<body>
				<h2>%s</h2>
				<p>%s</p>
				<hr>
				<p style="color: #666; font-size: 12px;">
					Это автоматическое уведомление от системы EduNite.<br>
					Пожалуйста, не отвечайте на это письмо.
				</p>
			</body>
			</html>
		`, notification.Title, notification.Message)
		textContent = fmt.Sprintf("%s\n\n%s\n\n---\nЭто автоматическое уведомление от системы EduNite.",
			notification.Title, notification.Message)
	}

	// Create email message
	message := gomail.NewMessage()
	message.SetHeader("From", s.config.Email.FromEmail)
	message.SetHeader("To", user.Email)
	message.SetHeader("Subject", subject)
	message.SetBody("text/plain", textContent)
	message.AddAlternative("text/html", htmlContent)

	// Send email
	if err := s.dialer.DialAndSend(message); err != nil {
		return fmt.Errorf("failed to send email to %s: %w", user.Email, err)
	}

	return nil
}

func (s *emailService) SendBulkNotificationEmails(notification *models.Notification, users []*models.User, emailTemplate *models.EmailTemplate) error {
	var errors []string

	for _, user := range users {
		if err := s.SendNotificationEmail(notification, user, emailTemplate); err != nil {
			errors = append(errors, fmt.Sprintf("User %d (%s): %v", user.ID, user.Email, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send emails to some users: %s", strings.Join(errors, "; "))
	}

	return nil
}

// processTemplate replaces template variables in the text
func (s *emailService) processTemplate(text string, variables map[string]interface{}) string {
	// First try Go template syntax
	tmpl, err := template.New("email").Parse(text)
	if err == nil {
		var buf bytes.Buffer
		if err := tmpl.Execute(&buf, variables); err == nil {
			return buf.String()
		}
	}

	// Fallback to simple string replacement for {{variable}} syntax
	result := text
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		dotPlaceholder := fmt.Sprintf("{{.%s}}", key)
		valueStr := fmt.Sprintf("%v", value)

		result = strings.ReplaceAll(result, placeholder, valueStr)
		result = strings.ReplaceAll(result, dotPlaceholder, valueStr)
	}

	return result
}

// stripHTML removes HTML tags from text (simple implementation)
func (s *emailService) stripHTML(html string) string {
	// Simple HTML tag removal - in production, consider using a proper HTML parser
	text := html
	text = strings.ReplaceAll(text, "<br>", "\n")
	text = strings.ReplaceAll(text, "<br/>", "\n")
	text = strings.ReplaceAll(text, "<br />", "\n")
	text = strings.ReplaceAll(text, "</p>", "\n\n")
	text = strings.ReplaceAll(text, "</div>", "\n")
	text = strings.ReplaceAll(text, "</h1>", "\n")
	text = strings.ReplaceAll(text, "</h2>", "\n")
	text = strings.ReplaceAll(text, "</h3>", "\n")
	text = strings.ReplaceAll(text, "</h4>", "\n")
	text = strings.ReplaceAll(text, "</h5>", "\n")
	text = strings.ReplaceAll(text, "</h6>", "\n")

	// Remove all remaining HTML tags
	for strings.Contains(text, "<") && strings.Contains(text, ">") {
		start := strings.Index(text, "<")
		end := strings.Index(text[start:], ">")
		if end == -1 {
			break
		}
		text = text[:start] + text[start+end+1:]
	}

	// Clean up multiple newlines
	for strings.Contains(text, "\n\n\n") {
		text = strings.ReplaceAll(text, "\n\n\n", "\n\n")
	}

	return strings.TrimSpace(text)
}
