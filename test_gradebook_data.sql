-- Test data for Thread Gradebook API
-- Тестовые данные для API журнала оценок потока

-- Note: This script assumes you have basic structure in place
-- Примечание: Этот скрипт предполагает, что у вас есть базовая структура

-- Insert test users (students and teacher) if they don't exist
INSERT INTO users (id, name, surname, email, username, password_hash, role, created_at, updated_at) 
VALUES 
    (101, 'Иван', 'Иванов', '<EMAIL>', 'ivan_ivanov', 'hash1', 'student', NOW(), NOW()),
    (102, 'Петр', 'Петров', '<EMAIL>', 'petr_petrov', 'hash2', 'student', NOW(), NOW()),
    (103, 'Анна', 'Сидорова', '<EMAIL>', 'anna_sidorova', 'hash3', 'student', NOW(), NOW()),
    (201, 'Преподаватель', 'Тестовый', '<EMAIL>', 'test_teacher', 'hash_teacher', 'teacher', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test course if it doesn't exist
INSERT INTO courses (id, title, description, created_at, updated_at)
VALUES (1, 'Тестовый курс', 'Курс для тестирования журнала оценок', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test semester if it doesn't exist
INSERT INTO semesters (id, name, start_date, end_date, created_at, updated_at)
VALUES (1, 'Тестовый семестр', '2024-01-01', '2024-06-30', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test thread if it doesn't exist
INSERT INTO threads (id, course_id, semester_id, teacher_id, title, max_students, created_at, updated_at)
VALUES (1, 1, 1, 201, 'Тестовый поток', 50, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Register students to the thread
INSERT INTO thread_registrations (thread_id, user_id, final_grade, created_at, updated_at)
VALUES 
    (1, 101, 47.0, NOW(), NOW()),
    (1, 102, 50.0, NOW(), NOW()),
    (1, 103, NULL, NOW(), NOW())  -- No final grade yet
ON CONFLICT (thread_id, user_id) DO UPDATE SET
    final_grade = EXCLUDED.final_grade,
    updated_at = NOW();

-- Insert test weeks for the thread
INSERT INTO weeks (id, thread_id, week_number, start_date, end_date, created_at, updated_at)
VALUES 
    (1, 1, 1, '2024-01-01', '2024-01-07', NOW(), NOW()),
    (2, 1, 2, '2024-01-08', '2024-01-14', NOW(), NOW()),
    (3, 1, 3, '2024-01-15', '2024-01-21', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test assignments
INSERT INTO assignments (id, week_id, title, description, due_date, max_points, type, created_at, updated_at)
VALUES 
    (101, 1, 'Домашнее задание 1', 'Первое домашнее задание', '2024-01-07 23:59:59', 12, 'task', NOW(), NOW()),
    (102, 2, 'Домашнее задание 2', 'Второе домашнее задание', '2024-01-14 23:59:59', 15, 'task', NOW(), NOW()),
    (103, 3, 'Итоговый проект', 'Финальный проект курса', '2024-01-21 23:59:59', 25, 'task', NOW(), NOW()),
    (104, 1, 'Информация о курсе', 'Информационное сообщение', '2024-12-31 23:59:59', 0, 'info', NOW(), NOW())  -- This should be excluded
ON CONFLICT (id) DO NOTHING;

-- Insert test submissions and grades
INSERT INTO assignment_submissions (id, assignment_id, user_id, submitted_at, file_urls, comment, score, feedback, created_at, updated_at)
VALUES 
    -- Ivan's submissions
    (1001, 101, 101, '2024-01-06 10:00:00', '["submission1.pdf"]', 'Мое решение задания 1', 12, 'Отличная работа!', NOW(), NOW()),
    (1002, 102, 101, '2024-01-13 15:30:00', '["submission2.pdf"]', 'Мое решение задания 2', 13, 'Хорошо, но есть недочеты', NOW(), NOW()),
    (1003, 103, 101, '2024-01-20 20:00:00', '["project.zip"]', 'Финальный проект', 22, 'Очень хорошая работа', NOW(), NOW()),
    
    -- Petr's submissions
    (1004, 101, 102, '2024-01-07 09:00:00', '["petr_hw1.pdf"]', 'Решение первого задания', 10, 'Неплохо, но можно лучше', NOW(), NOW()),
    (1005, 102, 102, '2024-01-14 12:00:00', '["petr_hw2.pdf"]', 'Решение второго задания', 15, 'Отлично!', NOW(), NOW()),
    (1006, 103, 102, '2024-01-21 18:00:00', '["petr_project.zip"]', 'Мой проект', 25, 'Превосходная работа!', NOW(), NOW()),
    
    -- Anna's submissions (incomplete - no submission for assignment 103)
    (1007, 101, 103, '2024-01-07 14:00:00', '["anna_hw1.pdf"]', 'Первое задание', 11, 'Хорошо', NOW(), NOW()),
    (1008, 102, 103, '2024-01-14 16:00:00', '["anna_hw2.pdf"]', 'Второе задание', 14, 'Хорошая работа', NOW(), NOW())
    -- Note: Anna has no submission for assignment 103, so no final grade yet
ON CONFLICT (id) DO NOTHING;

-- Verify the data
SELECT 'Threads' as table_name, COUNT(*) as count FROM threads WHERE id = 1
UNION ALL
SELECT 'Students in thread', COUNT(*) FROM thread_registrations WHERE thread_id = 1
UNION ALL
SELECT 'Assignments (task type)', COUNT(*) FROM assignments a JOIN weeks w ON a.week_id = w.id WHERE w.thread_id = 1 AND a.type = 'task'
UNION ALL
SELECT 'Submissions', COUNT(*) FROM assignment_submissions s JOIN assignments a ON s.assignment_id = a.id JOIN weeks w ON a.week_id = w.id WHERE w.thread_id = 1;

-- Show sample data
SELECT 'Sample thread data:' as info;
SELECT t.id, t.title, COUNT(tr.user_id) as student_count 
FROM threads t 
LEFT JOIN thread_registrations tr ON t.id = tr.thread_id 
WHERE t.id = 1 
GROUP BY t.id, t.title;

SELECT 'Sample assignments:' as info;
SELECT a.id, a.title, a.max_points 
FROM assignments a 
JOIN weeks w ON a.week_id = w.id 
WHERE w.thread_id = 1 AND a.type = 'task' 
ORDER BY w.week_number, a.id;

SELECT 'Sample students:' as info;
SELECT u.id, u.name, u.surname, tr.final_grade 
FROM users u 
JOIN thread_registrations tr ON u.id = tr.user_id 
WHERE tr.thread_id = 1 AND u.role = 'student' 
ORDER BY u.surname, u.name;
